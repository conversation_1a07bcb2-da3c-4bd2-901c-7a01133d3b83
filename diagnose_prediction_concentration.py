"""
Diagnose prediction concentration problem

This script analyzes the token distribution and model behavior
to understand why the model is predicting only one token.
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.dataset.dataset_bar_tokenized import BarTokenizedDataset
from pyqlab.models.gpt.bar_gpt4 import BarGpt4


def analyze_token_distribution(data_file: str):
    """Analyze the token distribution in the dataset"""
    print("=== Analyzing Token Distribution ===")
    
    # Create dataset with current configuration
    config = BarTokenizedDataset.get_default_config()
    config.block_size = 30
    config.tokenizer.n_bins = 100
    config.balance.gini_threshold = 0.6
    
    dataset = BarTokenizedDataset(config, data_file)
    
    print(f"Dataset info:")
    print(f"  Samples: {len(dataset)}")
    print(f"  Vocabulary size: {dataset.get_vocab_size()}")
    print(f"  Code count: {dataset.get_code_size()}")
    
    # Collect all tokens
    all_input_tokens = []
    all_target_tokens = []
    
    print("Collecting tokens...")
    for i in range(min(10000, len(dataset))):  # Sample first 10k
        if i % 1000 == 0:
            print(f"  Progress: {i}/{min(10000, len(dataset))}")
        
        code, x, x_mark, y = dataset[i]
        all_input_tokens.extend(x.numpy())
        all_target_tokens.extend(y.numpy())
    
    # Analyze input token distribution
    input_counter = Counter(all_input_tokens)
    target_counter = Counter(all_target_tokens)
    
    print(f"\nInput token distribution:")
    print(f"  Unique tokens: {len(input_counter)} / {dataset.get_vocab_size()}")
    print(f"  Most common tokens:")
    for token, count in input_counter.most_common(10):
        freq = count / len(all_input_tokens)
        print(f"    Token {token}: {count} ({freq:.2%})")
    
    print(f"\nTarget token distribution:")
    print(f"  Unique tokens: {len(target_counter)} / {dataset.get_vocab_size()}")
    print(f"  Most common tokens:")
    for token, count in target_counter.most_common(10):
        freq = count / len(all_target_tokens)
        print(f"    Token {token}: {count} ({freq:.2%})")
    
    # Calculate distribution metrics
    target_freqs = np.array(list(target_counter.values())) / len(all_target_tokens)
    gini = calculate_gini(target_freqs)
    entropy = -np.sum(target_freqs * np.log2(target_freqs + 1e-10))
    max_entropy = np.log2(len(target_counter))
    normalized_entropy = entropy / max_entropy
    
    print(f"\nDistribution metrics:")
    print(f"  Gini coefficient: {gini:.4f}")
    print(f"  Entropy: {entropy:.4f}")
    print(f"  Max entropy: {max_entropy:.4f}")
    print(f"  Normalized entropy: {normalized_entropy:.4f}")
    
    # Check for problematic patterns
    max_freq = max(target_freqs)
    if max_freq > 0.1:
        print(f"\n⚠️  High frequency token detected: {max_freq:.2%}")
        most_common_token = target_counter.most_common(1)[0][0]
        print(f"    Most common token: {most_common_token}")
    
    if len(target_counter) < dataset.get_vocab_size() * 0.1:
        print(f"\n⚠️  Low vocabulary utilization: {len(target_counter)}/{dataset.get_vocab_size()} ({len(target_counter)/dataset.get_vocab_size():.1%})")
    
    return {
        'input_counter': input_counter,
        'target_counter': target_counter,
        'gini': gini,
        'normalized_entropy': normalized_entropy,
        'vocab_utilization': len(target_counter) / dataset.get_vocab_size()
    }


def calculate_gini(frequencies):
    """Calculate Gini coefficient"""
    sorted_freq = np.sort(frequencies)
    n = len(sorted_freq)
    cumsum = np.cumsum(sorted_freq)
    return (n + 1 - 2 * np.sum(cumsum)) / n


def test_model_behavior(data_file: str):
    """Test model behavior with different configurations"""
    print("\n=== Testing Model Behavior ===")
    
    # Create small dataset for testing
    config = BarTokenizedDataset.get_default_config()
    config.block_size = 30
    config.tokenizer.n_bins = 50  # Smaller vocabulary
    config.balance.max_token_frequency = 0.05
    config.balance.gini_threshold = 0.5
    
    dataset = BarTokenizedDataset(config, data_file)
    
    print(f"Test dataset info:")
    print(f"  Samples: {len(dataset)}")
    print(f"  Vocabulary size: {dataset.get_vocab_size()}")
    
    # Create small model
    model = BarGpt4(
        block_size=config.block_size,
        code_size=dataset.get_code_size(),
        vocab_size=dataset.get_vocab_size(),
        n_layer=2,  # Very small model
        n_head=4,
        d_model=64,
        time_encoding='timeF',
        time_embed_type='time_feature',
        freq='t',
        pos_embed_type='rope',
        dropout=0.1
    )
    
    print(f"Model parameters: {model.get_num_params():,}")
    
    # Test with a few samples
    model.eval()
    predictions_list = []
    targets_list = []
    
    with torch.no_grad():
        for i in range(min(100, len(dataset))):
            code, x, x_mark, y = dataset[i]
            
            # Add batch dimension
            code = code.unsqueeze(0)
            x = x.unsqueeze(0)
            x_mark = x_mark.unsqueeze(0)
            y = y.unsqueeze(0)
            
            logits, loss = model(code, x, x_mark, y)
            predictions = torch.argmax(logits, dim=-1)
            
            predictions_list.extend(predictions.squeeze().numpy())
            targets_list.extend(y.squeeze().numpy())
    
    # Analyze predictions
    pred_counter = Counter(predictions_list)
    target_counter = Counter(targets_list)
    
    print(f"\nUntrained model predictions:")
    print(f"  Unique predictions: {len(pred_counter)} / {dataset.get_vocab_size()}")
    print(f"  Most common predictions:")
    for token, count in pred_counter.most_common(5):
        freq = count / len(predictions_list)
        print(f"    Token {token}: {count} ({freq:.2%})")
    
    print(f"\nTarget distribution in test samples:")
    print(f"  Unique targets: {len(target_counter)} / {dataset.get_vocab_size()}")
    print(f"  Most common targets:")
    for token, count in target_counter.most_common(5):
        freq = count / len(targets_list)
        print(f"    Token {token}: {count} ({freq:.2%})")
    
    # Check if model is already biased
    max_pred_freq = max(pred_counter.values()) / len(predictions_list)
    if max_pred_freq > 0.5:
        print(f"\n⚠️  Untrained model already shows prediction bias: {max_pred_freq:.2%}")
        print("    This suggests the problem is in the model architecture or initialization")
    else:
        print(f"\n✅ Untrained model shows reasonable diversity: max freq = {max_pred_freq:.2%}")


def suggest_improvements(analysis_results):
    """Suggest improvements based on analysis"""
    print("\n=== Suggested Improvements ===")
    
    gini = analysis_results['gini']
    entropy = analysis_results['normalized_entropy']
    vocab_util = analysis_results['vocab_utilization']
    
    suggestions = []
    
    if gini > 0.5:
        suggestions.append("1. Reduce vocabulary size (try n_bins=30-50)")
        suggestions.append("2. Increase max_token_frequency limit (try 0.03-0.05)")
        suggestions.append("3. Use stronger balancing (gini_threshold=0.3-0.4)")
    
    if entropy < 0.7:
        suggestions.append("4. Add diversity regularization (diversity_weight=0.3-0.5)")
        suggestions.append("5. Use label smoothing in loss function")
    
    if vocab_util < 0.2:
        suggestions.append("6. Check tokenizer mapping strategy (try 'adaptive')")
        suggestions.append("7. Reduce feature complexity")
    
    # Model-specific suggestions
    suggestions.append("8. Use smaller model (n_layer=2-3, d_model=64-96)")
    suggestions.append("9. Increase dropout (0.2-0.3)")
    suggestions.append("10. Lower learning rate (1e-5 to 5e-5)")
    suggestions.append("11. Add early stopping (patience=2-3)")
    suggestions.append("12. Use gradient clipping")
    
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    print(f"\nRecommended configuration:")
    print(f"  --n_bins 30")
    print(f"  --max_token_frequency 0.03")
    print(f"  --gini_threshold 0.3")
    print(f"  --diversity_weight 0.5")
    print(f"  --n_layer 2")
    print(f"  --d_model 64")
    print(f"  --dropout 0.3")
    print(f"  --lr 1e-5")
    print(f"  --early_stop 2")


def main():
    """Main diagnosis function"""
    data_file = "f:/hqdata/fut_top_min1.parquet"
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        print("Please ensure the data file exists.")
        return
    
    print("=== Diagnosing Prediction Concentration Problem ===\n")
    
    try:
        # Analyze token distribution
        analysis_results = analyze_token_distribution(data_file)
        
        # Test model behavior
        test_model_behavior(data_file)
        
        # Suggest improvements
        suggest_improvements(analysis_results)
        
        print(f"\n=== Diagnosis Complete ===")
        print(f"Next steps:")
        print(f"1. Try the recommended configuration above")
        print(f"2. Run: python train_improved_tokenizer.py")
        print(f"3. Monitor training diversity metrics closely")
        
    except Exception as e:
        print(f"❌ Diagnosis failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
