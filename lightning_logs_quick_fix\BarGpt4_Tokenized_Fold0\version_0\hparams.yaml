block_size: 20
class_weights: !!python/object/apply:torch._utils._rebuild_tensor_v2
- !!python/object/apply:torch.storage._load_from_bytes
  - !!binary |
    gAKKCmz8nEb5IGqoUBkugAJN6QMugAJ9cQAoWBAAAABwcm90b2NvbF92ZXJzaW9ucQFN6QNYDQAA
    AGxpdHRsZV9lbmRpYW5xAohYCgAAAHR5cGVfc2l6ZXNxA31xBChYBQAAAHNob3J0cQVLAlgDAAAA
    aW50cQZLBFgEAAAAbG9uZ3EHSwR1dS6AAihYBwAAAHN0b3JhZ2VxAGN0b3JjaApGbG9hdFN0b3Jh
    Z2UKcQFYDQAAADE2NzY4NzIwMzMzNjBxAlgDAAAAY3B1cQNLMk50cQRRLoACXXEAWA0AAAAxNjc2
    ODcyMDMzMzYwcQFhLjIAAAAAAAAAAACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/
    AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8A
    AIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAA
    gD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8=
- 0
- !!python/tuple
  - 50
- !!python/tuple
  - 1
- false
- !!python/object/apply:collections.OrderedDict
  - []
code_size: 6
d_model: 64
diversity_weight: 1.0
dropout: 0.3
freq: t
lr: 1.0e-05
n_head: 4
n_layer: 2
pos_embed_type: rope
time_embed_type: time_feature
time_encoding: timeF
vocab_size: 50
weight_decay: 0.1
