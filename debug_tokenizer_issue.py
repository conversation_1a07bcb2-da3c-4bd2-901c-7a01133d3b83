"""
Debug and fix the BarTokenizer issue

The problem is in the _independent_combine method which sums all feature tokens,
causing all tokens to have the same value.
"""

import os
import sys
import pandas as pd
import numpy as np
from collections import Counter

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer


def debug_feature_extraction(data_file: str):
    """Debug the feature extraction process"""
    print("=== Debugging Feature Extraction ===")
    
    # Load a small sample of data
    df = pd.read_parquet(data_file)
    
    # Take first security and first 100 rows
    first_code = df['code'].iloc[0]
    sample_df = df[df['code'] == first_code].head(100).copy()
    
    print(f"Sample data shape: {sample_df.shape}")
    print(f"Date range: {sample_df['datetime'].min()} to {sample_df['datetime'].max()}")
    
    # Create tokenizer
    tokenizer = BarTokenizer(
        mapping_strategy='quantile',
        balancing_strategy='none',  # Disable balancing for debugging
        n_bins=10,  # Small number for easier debugging
        features=['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
    )
    
    # Extract features manually
    features_df = tokenizer._extract_features(sample_df)
    
    print(f"\nExtracted features:")
    print(features_df.head())
    print(f"\nFeature statistics:")
    print(features_df.describe())
    
    # Check for NaN or infinite values
    print(f"\nNaN values per feature:")
    print(features_df.isnull().sum())
    
    print(f"\nInfinite values per feature:")
    for col in features_df.columns:
        inf_count = np.isinf(features_df[col]).sum()
        print(f"  {col}: {inf_count}")
    
    # Fit tokenizer
    tokenizer.fit(sample_df)
    
    # Check individual feature tokens
    print(f"\n=== Individual Feature Tokens ===")
    feature_tokens = {}
    for feature in tokenizer.features:
        if feature in features_df.columns and feature in tokenizer.feature_mappers:
            tokens = tokenizer.feature_mappers[feature].transform(features_df[feature].values)
            feature_tokens[feature] = tokens
            
            unique_tokens = np.unique(tokens)
            print(f"{feature}:")
            print(f"  Token range: {tokens.min()} to {tokens.max()}")
            print(f"  Unique tokens: {len(unique_tokens)} ({unique_tokens})")
            print(f"  Token distribution: {Counter(tokens)}")
    
    # Test combination methods
    print(f"\n=== Testing Combination Methods ===")
    
    # Method 1: Current (problematic) method
    combined_current = tokenizer._combine_feature_tokens(feature_tokens)
    print(f"Current method:")
    print(f"  Combined range: {combined_current.min()} to {combined_current.max()}")
    print(f"  Unique combined: {len(np.unique(combined_current))}")
    print(f"  Distribution: {Counter(combined_current)}")
    
    return feature_tokens, combined_current


def test_fixed_combination(feature_tokens: dict, n_bins: int = 10):
    """Test a fixed combination method"""
    print(f"\n=== Testing Fixed Combination Methods ===")
    
    # Method 1: Select one feature cyclically
    def cyclic_select(feature_tokens, n_bins):
        n_samples = len(list(feature_tokens.values())[0])
        features = list(feature_tokens.keys())
        combined = np.zeros(n_samples, dtype=np.int32)
        
        for i in range(n_samples):
            # Select feature based on position
            feature_idx = i % len(features)
            selected_feature = features[feature_idx]
            combined[i] = feature_tokens[selected_feature][i] + feature_idx * n_bins
        
        return combined
    
    # Method 2: Hash-based combination
    def hash_combination(feature_tokens, target_size=50):
        n_samples = len(list(feature_tokens.values())[0])
        combined = np.zeros(n_samples, dtype=np.int32)
        
        for i in range(n_samples):
            # Create tuple of all feature tokens at position i
            feature_tuple = tuple(tokens[i] for tokens in feature_tokens.values())
            # Hash and map to target range
            hash_value = hash(feature_tuple) % target_size
            combined[i] = hash_value
        
        return combined
    
    # Method 3: Weighted combination
    def weighted_combination(feature_tokens, n_bins):
        n_samples = len(list(feature_tokens.values())[0])
        combined = np.zeros(n_samples, dtype=np.int32)
        
        weights = [1, 2, 3, 4, 5]  # Different weights for different features
        
        for i, (feature, tokens) in enumerate(feature_tokens.items()):
            weight = weights[i] if i < len(weights) else 1
            combined += tokens * weight
        
        # Map to reasonable range
        combined = combined % (n_bins * len(feature_tokens))
        return combined
    
    # Test all methods
    methods = [
        ("Cyclic Select", cyclic_select),
        ("Hash Combination", hash_combination), 
        ("Weighted Combination", weighted_combination)
    ]
    
    for name, method in methods:
        try:
            if name == "Hash Combination":
                combined = method(feature_tokens, 50)
            else:
                combined = method(feature_tokens, n_bins)
            
            unique_count = len(np.unique(combined))
            print(f"{name}:")
            print(f"  Range: {combined.min()} to {combined.max()}")
            print(f"  Unique tokens: {unique_count}")
            print(f"  Sample values: {combined[:10]}")
            
            # Check distribution
            counter = Counter(combined)
            max_freq = max(counter.values()) / len(combined)
            print(f"  Max frequency: {max_freq:.2%}")
            
        except Exception as e:
            print(f"{name}: Failed - {str(e)}")


def create_simple_test_data():
    """Create simple test data for debugging"""
    print("\n=== Creating Simple Test Data ===")
    
    # Create simple synthetic data
    n_samples = 20
    dates = pd.date_range('2025-01-01', periods=n_samples, freq='1min')
    
    # Create simple price patterns
    base_price = 100
    prices = base_price + np.sin(np.arange(n_samples) * 0.1) * 2
    
    df = pd.DataFrame({
        'datetime': dates,
        'code': ['TEST001'] * n_samples,
        'open': prices,
        'high': prices + np.random.uniform(0, 1, n_samples),
        'low': prices - np.random.uniform(0, 1, n_samples),
        'close': prices + np.random.uniform(-0.5, 0.5, n_samples),
        'volume': np.random.randint(1000, 10000, n_samples)
    })
    
    # Ensure OHLC consistency
    df['high'] = df[['open', 'high', 'close']].max(axis=1)
    df['low'] = df[['open', 'low', 'close']].min(axis=1)
    
    print(f"Created test data: {df.shape}")
    print(df.head())
    
    # Test with simple data
    tokenizer = BarTokenizer(
        mapping_strategy='linear',  # Use linear for predictability
        balancing_strategy='none',
        n_bins=5,  # Very small for debugging
        features=['change', 'body']  # Only 2 features
    )
    
    # Extract features
    features_df = tokenizer._extract_features(df)
    print(f"\nSimple features:")
    print(features_df)
    
    # Fit and transform
    tokens = tokenizer.fit_transform(df)
    print(f"\nSimple tokens: {tokens}")
    print(f"Unique tokens: {np.unique(tokens)}")
    
    return df


def main():
    """Main debugging function"""
    data_file = "f:/hqdata/fut_top_min1.parquet"
    
    print("=== BarTokenizer Issue Debugging ===\n")
    
    # Test with simple synthetic data first
    create_simple_test_data()
    
    if os.path.exists(data_file):
        print(f"\n" + "="*50)
        # Debug with real data
        feature_tokens, combined_current = debug_feature_extraction(data_file)
        
        # Test fixed combination methods
        test_fixed_combination(feature_tokens, n_bins=10)
        
        print(f"\n=== Recommendations ===")
        print(f"1. The current _independent_combine method is flawed")
        print(f"2. It sums all feature tokens, causing concentration")
        print(f"3. Recommended fix: Use hash-based or cyclic selection")
        print(f"4. Alternative: Use much smaller n_bins (10-20)")
        print(f"5. Consider using only 1-2 most important features")
        
    else:
        print(f"Real data file not found: {data_file}")
        print("Only synthetic data test was performed.")


if __name__ == "__main__":
    main()
