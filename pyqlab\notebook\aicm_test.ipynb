import pandas as pd
from pyqlab.data.dataset.dataset_bar_tokenized import BarTokenizedDataset

bds = BarTokenizedDataset(
    
)

import pandas as pd

data_path = 'e:/hqdata/min1'
df = pd.read_parquet(f'{data_path}/fut/min1_202411.parquet')
df

import pandas as pd
col_names = [
                # RSI
                "FAST_RSI_ZSCORE", "FAST_RSI_DIRECT",
                "SLOW_RSI_ZSCORE", "SLOW_RSI_DIRECT",
                # MOM
                "FAST_MOM_ZSCORE", "FAST_MOM_DIRECT",
                "SLOW_MOM_ZSCORE", "SLOW_MOM_DIRECT",
                # FLRS
                "FAST_FLRS_ZSCORE", "FAST_FLRS_DIRECT",
                "SLOW_FLRS_ZSCORE", "SLOW_FLRS_DIRECT",
                # MLRS
                "FAST_MLRS_ZSCORE", "FAST_MLRS_DIRECT",
                "SLOW_MLRS_ZSCORE", "SLOW_MLRS_DIRECT",
                # NATR
                "FAST_NATR_ZSCORE", "FAST_NATR_DIRECT",
                "SLOW_NATR_ZSCORE", "SLOW_NATR_DIRECT",
            ]
df = pd.read_csv('E:/lab/RoboQuant/pylab/data/ft_all.all.00170607085233003.csv')

df[col_names].describe()

# df["SLOW_NATR_ZSCORE"]的分布图
import matplotlib.pyplot as plt

plt.hist(df["SLOW_NATR_ZSCORE"], bins=100, alpha=0.7, color='blue', ec='black')
plt.title("SLOW_NATR_ZSCORE distribution")
plt.xlabel("value")
plt.ylabel("frequency")
plt.show()


%reload_ext autoreload
%autoreload 2
from copy import deepcopy
import pandas as pd
from pyqlab.data.dataset.utils import get_vocab, idx2token
from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES, SNAPSHOT_CONTEXT

bar_set = get_vocab()

df = pd.DataFrame(bar_set, columns=['bar'], dtype=str, index=None)
df.to_csv('bar_set.csv')

df = pd.read_csv('f:/featdata/barenc/db2/bar_fut_top_min1_2025.csv')
df.head()


print(df.describe())

# 绘制df['bar']分布图
import matplotlib.pyplot as plt
plt.hist(df['bar'], bins=100, alpha=0.7, color='blue', ec='black')
plt.title("bar distribution")
plt.xlabel("value")
plt.ylabel("frequency")
plt.show()

df = pd.read_csv('f:/featdata/barenc/db2/bar_fut_top_min1.tmp.csv')
print(df.describe())

df.describe()

import pandas as pd
years = range(2023, 2025)
dfs = pd.DataFrame()
for year in years:
    df = pd.read_csv(f'f:/featdata/barenc/bar_fut_main_min5_{year}.csv')
    print(df.shape)
    df = df[(pd.to_datetime(df['timestamp'], unit='s') + pd.Timedelta(hours=8)).dt.hour >= 9]
    print(df.shape)
    df.to_csv(f'f:/featdata/barenc/bar_fut_main_min5_{year}.csv', index=False)


import pandas as pd
years = range(2010, 2025)
dfs = pd.DataFrame()
for year in years:
    df = pd.read_csv(f'f:/featdata/barenc/bak/bar_fut_sf_min5_{year}.csv')
    dfs = pd.concat([dfs, df], axis=0)
    # 通过columns=['symbol','timestamp']去重
    # dfs = dfs.drop_duplicates(subset=['symbol', 'timestamp'], keep='last')
    # print(year, dfs.shape)
# dfs['timestamp'] = pd.to_datetime(dfs['timestamp'], unit='s') + pd.Timedelta(hours=8)
dfs = dfs[(pd.to_datetime(dfs['timestamp'], unit='s') + pd.Timedelta(hours=8)).dt.time >= pd.to_datetime('09:30:00').time()]
dfs.reset_index(drop=True, inplace=True)
dfs.to_csv('f:/featdata/barenc/bar_fut_sf_min5.csv', index=False)
print(dfs.shape)
dfs

df=pd.read_parquet('e:/featdata/main/ffs_ct.main.2024.parquet')
df = df[SNAPSHOT_CONTEXT[16:-8]]
df.describe().to_csv('e:/featdata/main/ffs_ct.main.2024.csv')

df=pd.read_parquet('e:/hqdata/tick/2024/SF202409.parquet')
df.shape

df=df.groupby(['code', df['datetime'].dt.floor('1s')], as_index=False).last()
df.sort_values(['datetime', 'code'], inplace=True)
df.reset_index(inplace=True, drop=True)
print(df.shape)
df.head(20)

df1 = df.groupby(['code', df['datetime'].dt.floor('10s')]).apply(lambda group: group.loc[group['price'].idxmin()])
df1.reset_index(inplace=True, drop=True)
df2 = df.groupby(['code', df['datetime'].dt.floor('10s')]).apply(lambda group: group.loc[group['price'].idxmax()])
df2.reset_index(inplace=True, drop=True)
df=pd.concat([df1, df2])
df.sort_values(['datetime', 'code'], inplace=True)
df.reset_index(inplace=True, drop=True)
print(df.shape)
print(df.head(20))

import sys
import pandas as pd
sys.path.append("d:/QuantLab")
from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode

ds=DataSource(RunMode.passive)
print(ds.get_run_dir())

blkname=["ZLQH", "ZLLX","主选期货", "主力9999", "股指板块", "沪深300", "中证500", "中证1000"]
zlqh = ds.get_block_data(blkname[1])
print(zlqh)

gzbk = ds.get_block_data(blkname[4])
for i in range(10):
    print(gzbk[i], ds.get_stk_code_index(gzbk[i]))


zllx = ds.get_block_data(blkname[1])
for i in range(10):
    code = zllx[i][0:len(zllx[i])-7]
    print(code, ds.get_fut_code_index(code))

# 读取文华财经的主连数据，用8888代码，长度不能超过6000
symbol = 'I8888.DC' # 'I2501.DC'
hist_data=ds.get_history_data(symbol, 0, [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close], 
                              BarSize.day, DoRight.none)
print(hist_data.shape)
df = pd.DataFrame(hist_data)
df.columns = ['datetime', 'open', 'high', 'low', 'close']
df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)
df


symbol = '000009.SZ'
hist_data=ds.get_history_data(symbol, 0, [BarData.datetime, BarData.open, BarData.high, BarData.low, BarData.close, BarData.volume], 
                              BarSize.min5, DoRight.none)
print(hist_data.shape)
df = pd.DataFrame(hist_data)
df.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
df['datetime'] = pd.to_datetime(df['datetime'], unit='s') + pd.Timedelta(hours=8)
df['code'] = symbol
df

from pyqlab.data.dataset.pipeline import Pipeline
import pandas as pd
# bars vocabulary
# bars=Pipeline.get_vocab()
# pd.DataFrame(bars).to_csv("bars.csv")

data_path = "e:/featdata/main/ffs_ct.main.2024.parquet"
data = pd.read_parquet(data_path)

data.describe()


%load_ext autoreload
%autoreload 2

from functools import partial
import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES

feat_path = 'e:/featdata'

year = 2023
# lf_df = pd.read_parquet(f'{feat_path}/ffs_lf.main.{year}.parquet')
sf_df = pd.read_parquet(f'{feat_path}/ffs_sf.main.{year}.parquet')
# mf_df = pd.read_parquet(f'{feat_path}/ffs_mf.main.{year}.parquet')
# ct_df = pd.read_parquet(f'{feat_path}/ffs_ct.main.{year}.parquet')

fut_codes_dict = {code: i for i, code in enumerate(MAIN_FUT_CODES)}
sf_df['code_encoded'] = sf_df['code'].apply(lambda x: fut_codes_dict[x])
sf_df['change'] = sf_df['change'].astype(np.float32)
sf_df['change'].fillna(0.0, inplace=True)
# sf_df['long_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)
# sf_df['short_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)
sf_df['change'] = sf_df['change'] * 100.0
sf_df['change'] = sf_df.loc[(sf_df['change'] > -1) & (sf_df['change'] < 1), 'change']

# change列标签化，用于分类问题<-0.25, -0.25~0.25, >0.25
sf_df['label'] = sf_df['change'].apply(lambda x: 0 if x < -0.25 else 2 if x > 0.25 else 1)

sf_df['label'].value_counts()

# 画出change的分布图
import matplotlib.pyplot as plt
step=2
midd=4
bins = [x/100.0 for x in range(-100, 100+step, step)]
bins = bins[:100//step-midd] + [-0.07, 0.07] + bins[-100//step+midd:]
# bins = [-np.inf] + bins + [np.inf]
plt.hist(sf_df['change'], bins=bins, label='change')

len(bins)

# 使用自定义分箱
sf_df['change_bins'] = pd.cut(sf_df['change'], bins=bins, labels=False)
sf_df['change_bins'].fillna(0, inplace=True)
sf_df['change_bins'] = sf_df['change_bins'].astype(np.int32)


# change2列change按分箱区间映射取值
sf_df['change2'] = sf_df['change_bins'].apply(lambda x: bins[x])


lf_df['code'].unique().shape, sf_df['code'].unique().shape, ct_df['code'].unique().shape

lf_df

sf_df

# lf_df.head(100).to_csv(f'{feat_path}/lf_df.csv')
sf_df.head(300).to_csv(f'{feat_path}/sf_df.csv')
# ct_df.head(100).to_csv(f'{feat_path}/ct_df.csv')

ct_df

ct_df["DAYOFWEEK"].value_counts()

ct_df["HOUR"].value_counts()

# 通过date列timestamp，并将时区转换为北京时间
ct_df['datetime'] = pd.to_datetime(ct_df['date'] + 28800, unit='s')
ct_df['DAYOFWEEK'] = pd.to_datetime(ct_df['date'] + 28800, unit='s').dt.dayofweek
ct_df['HOUR'] = pd.to_datetime(ct_df['date'] + 28800, unit='s').dt.hour


ct_df

sf_df.describe(percentiles=[0.01, 0.05, 0.95, 0.99])

# 统计BAND_EXPAND的分布
sf_df['BAND_EXPAND_2'].describe(percentiles=[0.01, 0.05, 0.95, 0.99])

sf_df['BAND_EXPAND_2'].hist(bins=100)

sf_df.loc[sf_df['BAND_EXPAND_2']>15]

ct_df

SEL_LONG_FACTOR_NAMES = [ # Slow period factor
    # "MACD", "MACD_DIFF", "MACD_DEA", "MOM", "RSI",

    "LR_SLOPE_FAST",
    "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    # "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    # "SQUEEZE_ZERO_BARS", 
    # "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    # "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    # "BAND_POSITION", "BAND_WIDTH",
    # "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    # "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    # "TREND_LEVEL"
]

SEL_SHORT_FACTOR_NAMES = [ # Fast period factor
    # "VOLUME", # 在RangeBar下，Volume是Bar的时长seconds
    "MACD", "MACD_DIFF", "MACD_DEA", "RSI",

    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE",
    "TREND_BARS",
    "TREND_INBARS",
    "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]

SEL_CONTEXT_FACTOR_NAMES = [
  "STDDEV_RNG", "SHORT_RANGE",
  "FAST_QH_RSI", "FAST_QH_ZSCORE", "FAST_QH_DIRECT",
  "FAST_QH_NATR", "FAST_QH_NATR_ZSCORE", "FAST_QH_NATR_DIRECT",
  "FAST_QH_MOM", "FAST_QH_MOM_ZSCORE", "FAST_QH_MOM_DIRECT",
]

def _long_factor_select(n):
    if len(SEL_LONG_FACTOR_NAMES) == 0:
        return 0
    if ALL_FACTOR_NAMES[n] in SEL_LONG_FACTOR_NAMES:
        if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
            return 2
        else:
            return 1
    return 0

def _short_factor_select(n):
    if len(SEL_SHORT_FACTOR_NAMES) == 0:
        return 0
    if ALL_FACTOR_NAMES[n] in SEL_SHORT_FACTOR_NAMES:
        if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
            return 2
        else:
            return 1
    return 0

def _factor_select_name(sel_list):
    sel_name=[]
    if len(sel_list) == 0:
        return sel_name
    for n in range(len(ALL_FACTOR_NAMES)):
        if sel_list[n] > 0:
            sel_name.append(ALL_FACTOR_NAMES[n])
    return sel_name

def _context_select_name(sel_list):
    sel_name=[]
    if len(sel_list) == 0:
        return sel_name
    for n in range(len(SNAPSHOT_CONTEXT)):
        if sel_list[n] > 0:
            sel_name.append(SNAPSHOT_CONTEXT[n])
    return sel_name

def _get_factor_cols(factor_type="lf"):
    """
    因子列名称
    """
    col_names = []
    if factor_type == "lf":
        for name in SEL_LONG_FACTOR_NAMES:
            if name in TWO_VAL_FACTOR_NAMES:
                col_names.append(f"{name}_1")
                col_names.append(f"{name}_2")
            else:
                col_names.append(f"{name}_2")

    if factor_type == "sf":
        for name in SEL_SHORT_FACTOR_NAMES: # SEL_SHORT_FACTOR_NAMES:
            if name in TWO_VAL_FACTOR_NAMES:
                col_names.append(f"{name}_1")
                col_names.append(f"{name}_2")
            else:
                col_names.append(f"{name}_2")

    if factor_type == "ct":
        col_names.extend(SEL_CONTEXT_FACTOR_NAMES)

    return col_names

col_names = _get_factor_cols("lf")+_get_factor_cols("sf")+_get_factor_cols("ct")
print(len(col_names))
col_names

sel_lf = [_long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
sel_name = _factor_select_name(sel_lf)
print(sel_name)
print(len(sel_name), len(SEL_LONG_FACTOR_NAMES))

sel_sf = [_short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]
sel_name = _factor_select_name(sel_sf)
print(sel_name)
print(len(sel_name), len(SEL_SHORT_FACTOR_NAMES))

sf_df['change'] = sf_df['change'].astype('float')
sf_df['label_long'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)
sf_df['label_short'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)

col_names = _get_factor_cols(factor_type="sf")
col_names += ['code', 'date', 'change', 'label_long', 'label_short']
sel_sf_df = sf_df[col_names]


sel_sf_df

sel_sf_df.reset_index(drop=True, inplace=True)

sel_sf_df

# 计算绝对值最大的行序号
rolling_max_index = sel_sf_df['change'].rolling(window=5).apply(lambda x: abs(x).idxmax())


rolling_max_index


# 移动窗口的第一个值会返回 NaN，所以可以使用 fillna 方法填充为 0 或其他合适的值
rolling_max_index = rolling_max_index.fillna(0)

# 将结果转换为整数
rolling_max_index = rolling_max_index.astype(int)


for i in range(len(rolling_max_index)):
    if rolling_max_index[i] == sel_sf_df.index[i]:
        print(rolling_max_index[i])


print(rolling_max_index.head(50))

from functools import partial

def standardize(group, means, stds):
    code = group.name
    mean = means.loc[code]
    std = stds.loc[code]
    group = (group - mean) / std
    # print(code, mean, std)
    return group

lf_mean = pd.read_csv(f'{feat_path}/lf_mean.csv', index_col=0)
lf_std = pd.read_csv(f'{feat_path}/lf_std.csv', index_col=0)
sf_mean = pd.read_csv(f'{feat_path}/sf_mean.csv', index_col=0)
sf_std = pd.read_csv(f'{feat_path}/sf_std.csv', index_col=0)
ct_mean = pd.read_csv(f'{feat_path}/ct_mean.csv', index_col=0)
ct_std = pd.read_csv(f'{feat_path}/ct_std.csv', index_col=0)
print(sf_mean.shape, sf_std.shape)

le = LabelEncoder()
sf_df['code_encoded'] = le.fit_transform(sf_df['code'].values)

# 生成模型输入数据配置文件
# 放在数据处理的前面，以保证因子顺序与系统一致
# self._dump_input_param_json()
is_class = False
direct = 'long'
if is_class: # 分类问题，生成标签
    print("-----分类问题-----")
    if direct == 'long':
        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)
    elif direct == 'short':
        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)
    else:
        raise ValueError(f"direct {direct} is not supported")
else:
    print("-----非分类问题-----")
    sf_df['label'] = sf_df.loc[:, 'change']



col_names = _get_factor_cols(factor_type="lf")
if len(col_names) > 0:
    lf_df = lf_df[col_names + ['code']]
    df_mean = lf_mean[col_names]
    df_std = lf_std[col_names]
    partial_func = partial(standardize, means=df_mean, stds=df_std)
    df_standardized = lf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
    df_standardized.fillna(0.0, inplace=True)
    df_standardized.reset_index(drop=False, inplace=True)
    # df_standardized.sort_values(by=['code'], inplace=True)
    lf_df = df_standardized[col_names]
else:
    lf_df = pd.DataFrame()


lf_df


col_names = _get_factor_cols(factor_type="sf")
if len(col_names) > 0:
    sf_df = sf_df[col_names + ['code', 'date', 'change', 'code_encoded', 'label']]
    df_mean = sf_mean[col_names]
    df_std = sf_std[col_names]
    partial_func = partial(standardize, means=df_mean, stds=df_std)
    df_standardized = sf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
    df_standardized.fillna(0.0, inplace=True)
    df_standardized.reset_index(drop=False, inplace=True)
    # df_standardized.sort_values(by=['code'], inplace=True)
    sf_df[col_names] = df_standardized[col_names]
else:
    sf_df = pd.DataFrame()


sf_df


col_names = _get_factor_cols(factor_type="ct")
if len(col_names) > 0:
    ct_df = ct_df[col_names + ['code']]
    df_mean = ct_mean[col_names]
    df_std = ct_std[col_names]
    partial_func = partial(standardize, means=df_mean, stds=df_std)
    df_standardized = ct_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)
    df_standardized.fillna(0.0, inplace=True)
    df_standardized.reset_index(drop=False, inplace=True)
    # df_standardized.sort_values(by=['code'], inplace=True)
    ct_df = df_standardized[col_names]
else:
    ct_df = pd.DataFrame()


print(lf_df)
print(sf_df)
print(ct_df)


ft_df = pd.concat([lf_df, sf_df, ct_df], axis=1)
print(ft_df.columns.tolist())
print(f"\n===============\n\nft{ft_df.shape} lf{lf_df.shape} sf{sf_df.shape} ct{ct_df.shape}\n\n================\n")
print(ft_df)



# 合并后清除数据
# ft_df.dropna(axis=0, how='any', inplace=True)
ft_df.fillna(0.0, inplace=True)
if 'RSI_2' in ft_df.columns:
    ft_df = ft_df[ft_df['RSI_2'] != 0.0]
if 'FAST_QH_NATR_ZSCORE' in ft_df.columns:
    ft_df = ft_df[ft_df['FAST_QH_NATR_ZSCORE'] != 0.0]

lb_df = ft_df[['code', 'date', 'change', 'code_encoded', 'label']]
lb_df.reset_index(drop=True, inplace=True)

ft_df.drop(['code', 'date', 'change', 'code_encoded', 'label'], axis=1, inplace=True)
ft_df = ft_df.astype(np.float32)

print(f"\n===============\n\nlb{lb_df.shape} ft{ft_df.shape}\n\n================\n")
print(ft_df)
print(lb_df)
print(direct)
print(lb_df['label'].value_counts())

data1 = ft_df.values
data2 = lb_df.values[:, -1]
data3 = lb_df.values[:, -2]

all_col_names = df_mean.columns.to_list()
col_names = _get_factor_cols(factor_type="sf")
# all_col_names在col_names中的索引
sel_index = [all_col_names.index(name) for name in col_names]
print(len(sel_index), sel_index)


df_mean = df_mean[col_names]
df_std = df_std[col_names]
print(df_mean.shape, df_std.shape)

sel_sf_df2 = sel_sf_df[['code', 'date'] + col_names]
sel_sf_df2

# 创建一个偏函数
my_partial_func = partial(standardize, means=df_mean, stds=df_std)
df_standardized = sel_sf_df2.groupby('code')[col_names].apply(my_partial_func)
# df_standardized.reset_index(drop=False, inplace=True)
# df_standardized.fillna(0.0, inplace=True)

df_standardized



df_stand = df_standardized.loc[(df_standardized['RSI_2'] < 3) & (df_standardized['RSI_2'] > -3)]

df_stand

df_stand.describe()

df_standardized.describe().to_csv(f'{feat_path}/sf_standardized_describe.csv')

sel_sf_df[col_names] = df_standardized[col_names]

sel_sf_df

# 将因子数据按CODE分组求均值和标准差
sf_df_mean = sel_sf_df.groupby('code').mean()
sf_df_std = sel_sf_df.groupby('code').std()
print(sf_df_mean.shape, sf_df_std.shape)

# TODO: 由于长假或主力期货合约变更，导致部分合约数据有很大跳空，行情失真，因此需要去除这些数据
# 1. 选择特定Factor（不受合约不太影响，如RSI等），计算统计值
# 2. 选择统计值在一定范围内的数据

import json
import pandas as pd

rpt_path = 'd:/RoboQuant2/rpt'
json_file = 'model_test_202306172311.json'
with open(f'{rpt_path}/{json_file}') as file:
    # Load the JSON data
    data = json.load(file)
models = data['models']
data.pop('models')


dfs = pd.DataFrame()
for code in data.keys():
    df = pd.DataFrame(data[code], columns=['change'] + models)
    df ["change"] = df["change"].shift(-1)
    df.dropna(inplace=True)
    df = df.loc[df['change'] != 0.0, :]
    df.insert(0, 'code', code)
    dfs = pd.concat([dfs, df], axis=0)
# 删除loc.columns[2:]列所有列元素都为0的列
# dfs = dfs.loc[:, dfs[dfs.columns[2:]].sum(axis=0) > 0.0]
# # 删除所有行元素都为0的行
dfs = dfs.loc[dfs[dfs.columns[2:]].sum(axis=1) > 0.0, :]
dfs.reset_index(drop=True, inplace=True)
print(dfs.shape)
dfs.to_csv(f'{rpt_path}/{json_file}.csv', index=False)
dfs

dfs.describe(percentiles=[0.75, 0.95, 0.98, 0.99])

import pandas as pd

data = {
    'Column1': [1, 2, 3, 4, 5],
    'Column2': [3, 4, 5, 25, 30],
    'Column3': [0.5, 1.5, 2.5, 3.5, 4.5]
}

df = pd.DataFrame(data)
print(df)

# 设置阈值
threshold = 3

# 创建逻辑掩码，筛选值大于阈值的行
mask = df > threshold

# 使用掩码剔除不满足条件的行
filtered_df = df[~mask.any(axis=1)]

print(filtered_df)

import numpy as np

# 创建一个示例矩阵
matrix = np.array([[1, 2, 0.5],
                   [2, 3, 1.5],
                   [3, 4, 2.5],
                   [4, 25, 3.5],
                   [5, 30, 4.5]])

print("Original matrix:")
print(matrix)

# 设置阈值
threshold_lower = 2
threshold_upper = 4

# 创建逻辑掩码，同时满足两个条件
mask_lower = matrix[:, 1] > threshold_lower
mask_upper = matrix[:, 1] < threshold_upper
combined_mask = mask_lower & mask_upper

# 使用掩码剔除不满足条件的行
filtered_matrix = matrix[combined_mask]

print("Filtered matrix:")
print(filtered_matrix)

len(filtered_matrix)

import numpy as np

# 创建一个示例矩阵
matrix = np.array([[1, 2, 0.5],
                   [2, 3, 1.5],
                   [3, 4, 2.5],
                   [4, 25, 3.5],
                   [5, 30, 4.5]])

print("Original matrix:")
print(matrix)
(matrix > 3).any() or (matrix < -3).any()



import pandas as pd

data_path = 'e:/hqdata/tick/2022'
df = pd.read_parquet(f'{data_path}/SF202201.parquet', engine='fastparquet')

df.sort_values(by=['datetime'], inplace=True)
df.reset_index(drop=True, inplace=True)

df.loc[df['code'] == 'IF9999.SF', :]

from datetime import datetime, date
import time
import pytz
import pandas as pd
import sys
import io

sys.path.append("d:/QuantLab")
from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs

class MarketCsKvDB():
    def __init__(self,
        key_prefix='mkt_cs:', #fsfs 股指期货因子 ffs 商品期货因子 mkt_cs: 市场截面指标
        years=[],
        dbfile="d:/RoboQuant2/store/kv.db",
        save_path="e:/featdata",
        save_file="",
    ) -> None:
        self.years = years
        self._dbfile=dbfile
        self._save_path = save_path
        self._save_file = save_file
        self._db=None
        self._key_prefix=key_prefix
        self._keys=[]
        self._values=[]
        self._ls_col_names=[]
        self._ct_col_names=[]
        self._tz=pytz.timezone('Asia/Shanghai')

    def open_db(self, mode):
        if self._db:
            self.close_db()
        
        try:
            self._db=create_db("leveldb", self._dbfile, mode)
        except:
            raise 'Fail to open db!'

    def close_db(self):
        if not self._db:
            raise "not db open."
        self._db.close()
        del self._db
        self._db=None

    def load_all_keys(self):
        if not self._db:
            raise "first open a db."
        self._keys.clear()
        cursor = self._db.new_cursor()
        while cursor.valid():
            key = cursor.key().decode('gbk', 'ignore')
            if len(key) >= 16 and key[0:len(self._key_prefix)] == self._key_prefix: #'ffs:'
                self._keys.append(key)
                self._values.append(cursor.value().decode('gbk', 'ignore'))
                print
            cursor.next()
        del cursor

mcs = MarketCsKvDB(key_prefix='mkt_cs:', dbfile="d:/RoboQuant2/store/kv.db")
mcs.open_db(Mode.read)
mcs.load_all_keys()


data = mcs._values[0].split(',')
# df = pd.read_csv(io.StringIO(data), header=None)



data

