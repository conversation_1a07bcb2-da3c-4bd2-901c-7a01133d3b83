"""
Improved training script with better parameters to solve prediction concentration

Key improvements:
1. Smaller vocabulary size (50 instead of 500)
2. Stronger diversity regularization
3. Better data balancing
4. More aggressive early stopping
"""

import os
import sys
import argparse

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def main():
    """Run improved training with optimized parameters"""
    
    # Check if data file exists
    data_file = "f:/hqdata/fut_top_min1.parquet"
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        print("Please ensure the data file exists or modify the path in this script.")
        return
    
    print("=== Improved BarTokenizer Training ===")
    print("Key improvements:")
    print("1. Smaller vocabulary (50 tokens)")
    print("2. Stronger diversity regularization (weight=0.3)")
    print("3. Better data balancing (max_freq=0.05)")
    print("4. More aggressive early stopping")
    print("")
    
    # Build command
    cmd = [
        "python", "train_bar_gpt4_with_tokenizer_en.py",
        "--data_file", data_file,
        "--data_path", "f:/hqdata",
        "--block_size", "30",
        
        # BarTokenizer parameters - SMALLER VOCABULARY
        "--mapping_strategy", "quantile",
        "--balancing_strategy", "frequency", 
        "--n_bins", "50",  # Reduced from 100 to 50
        "--max_token_frequency", "0.05",  # Reduced from 0.08 to 0.05
        "--gini_threshold", "0.5",  # Reduced from 0.6 to 0.5
        
        # Model parameters - SMALLER MODEL
        "--n_layer", "3",  # Reduced from 4 to 3
        "--n_head", "6",   # Reduced from 8 to 6
        "--d_model", "96", # Reduced from 128 to 96
        "--time_encoding", "timeF",
        "--time_embed_type", "time_feature",
        "--pos_embed_type", "rope",
        "--dropout", "0.2",  # Increased from 0.1 to 0.2
        
        # Training parameters - STRONGER REGULARIZATION
        "--batch_size", "32",  # Smaller batch size
        "--lr", "5e-5",        # Lower learning rate
        "--weight_decay", "0.05",  # Higher weight decay
        "--max_epochs", "5",   # Fewer epochs
        "--k_folds", "3",
        "--early_stop", "3",   # More aggressive early stopping
        "--min_delta", "5e-3", # Larger min delta
        "--diversity_weight", "0.3",  # STRONG diversity regularization
        
        # Other parameters
        "--num_workers", "0",
        "--seed", "42",
        "--log_dir", "lightning_logs_improved",
        "--use_class_weights"
    ]
    
    print("Running command:")
    print(" ".join(cmd))
    print("")
    
    # Run training
    import subprocess
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ Training completed successfully!")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Training failed with error code: {e.returncode}")
        print("Please check the error messages above.")
        
    except FileNotFoundError:
        print("\n❌ Could not find the training script.")
        print("Please ensure train_bar_gpt4_with_tokenizer_en.py is in the current directory.")


def run_comparison_experiments():
    """Run multiple experiments with different configurations"""
    
    data_file = "f:/hqdata/fut_top_min1.parquet"
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return
    
    experiments = [
        {
            "name": "Small_Vocab_Strong_Reg",
            "n_bins": "30",
            "diversity_weight": "0.5",
            "max_token_frequency": "0.03",
            "dropout": "0.3",
            "log_dir": "logs_small_vocab_strong_reg"
        },
        {
            "name": "Medium_Vocab_Medium_Reg", 
            "n_bins": "50",
            "diversity_weight": "0.3",
            "max_token_frequency": "0.05",
            "dropout": "0.2",
            "log_dir": "logs_medium_vocab_medium_reg"
        },
        {
            "name": "Large_Vocab_Light_Reg",
            "n_bins": "80",
            "diversity_weight": "0.1",
            "max_token_frequency": "0.08",
            "dropout": "0.1",
            "log_dir": "logs_large_vocab_light_reg"
        }
    ]
    
    for i, exp in enumerate(experiments):
        print(f"\n=== Experiment {i+1}/3: {exp['name']} ===")
        
        cmd = [
            "python", "train_bar_gpt4_with_tokenizer_en.py",
            "--data_file", data_file,
            "--data_path", "f:/hqdata",
            "--block_size", "30",
            
            # BarTokenizer parameters
            "--mapping_strategy", "quantile",
            "--balancing_strategy", "frequency",
            "--n_bins", exp["n_bins"],
            "--max_token_frequency", exp["max_token_frequency"],
            "--gini_threshold", "0.5",
            
            # Model parameters
            "--n_layer", "3",
            "--n_head", "6", 
            "--d_model", "96",
            "--time_encoding", "timeF",
            "--time_embed_type", "time_feature",
            "--pos_embed_type", "rope",
            "--dropout", exp["dropout"],
            
            # Training parameters
            "--batch_size", "32",
            "--lr", "5e-5",
            "--weight_decay", "0.05",
            "--max_epochs", "5",
            "--k_folds", "3",
            "--early_stop", "3",
            "--min_delta", "5e-3",
            "--diversity_weight", exp["diversity_weight"],
            
            # Other parameters
            "--num_workers", "0",
            "--seed", "42",
            "--log_dir", exp["log_dir"],
            "--use_class_weights"
        ]
        
        print("Configuration:")
        print(f"  Vocabulary size: {exp['n_bins']}")
        print(f"  Diversity weight: {exp['diversity_weight']}")
        print(f"  Max token frequency: {exp['max_token_frequency']}")
        print(f"  Dropout: {exp['dropout']}")
        print("")
        
        import subprocess
        try:
            result = subprocess.run(cmd, check=True, capture_output=False)
            print(f"✅ Experiment {exp['name']} completed successfully!")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Experiment {exp['name']} failed with error code: {e.returncode}")
            continue
            
        except FileNotFoundError:
            print("❌ Could not find the training script.")
            break
    
    print("\n=== All experiments completed ===")
    print("Check the following log directories for results:")
    for exp in experiments:
        print(f"  {exp['log_dir']} - {exp['name']}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Improved BarTokenizer training")
    parser.add_argument("--mode", type=str, default="single", 
                       choices=["single", "comparison"],
                       help="Run single experiment or comparison experiments")
    
    args = parser.parse_args()
    
    if args.mode == "single":
        main()
    elif args.mode == "comparison":
        run_comparison_experiments()
