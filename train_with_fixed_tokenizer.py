"""
Training script using Fixed BarTokenizer

This script uses the improved tokenizer to solve prediction concentration.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_bar_tokenizer import FixedBarTokenizer
from train_bar_gpt4_with_tokenizer_en import main as original_main
import argparse

# Monkey patch the tokenizer
import pyqlab.data.dataset.dataset_bar_tokenized as dataset_module
dataset_module.BarTokenizer = FixedBarTokenizer

def main():
    """Main function with fixed tokenizer"""
    parser = argparse.ArgumentParser(description='Train with Fixed BarTokenizer')
    
    # Add all the same arguments as original script
    parser.add_argument('--data_file', type=str, required=True)
    parser.add_argument('--combination_method', type=str, default='hash',
                       choices=['hash', 'cyclic', 'weighted', 'independent'])
    parser.add_argument('--target_vocab_size', type=int, default=50)
    parser.add_argument('--n_bins', type=int, default=20)
    parser.add_argument('--max_epochs', type=int, default=3)
    parser.add_argument('--diversity_weight', type=float, default=0.5)
    
    args = parser.parse_args()
    
    print(f"Using Fixed BarTokenizer with {args.combination_method} combination")
    print(f"Target vocabulary size: {args.target_vocab_size}")
    
    # Set up arguments for original training function
    # (This would need to be adapted based on the actual training script)
    
    # Run training with fixed tokenizer
    # original_main(args)

if __name__ == "__main__":
    main()
