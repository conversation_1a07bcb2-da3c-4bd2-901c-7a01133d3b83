"""
Quick fix for the prediction concentration problem

This script provides immediate solutions without modifying the core BarTokenizer.
"""

import os
import sys
import subprocess

def run_with_minimal_config():
    """Run training with minimal configuration to avoid concentration"""
    
    data_file = "f:/hqdata/fut_top_min1.parquet"
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return
    
    print("=== Quick Fix Training ===")
    print("Using minimal configuration to solve prediction concentration:")
    print("1. Very small vocabulary (n_bins=10)")
    print("2. Only 2 most important features")
    print("3. Strong diversity regularization")
    print("4. Small model with high dropout")
    print("")
    
    # Build command with minimal settings
    cmd = [
        "python", "train_bar_gpt4_with_tokenizer_en.py",
        "--data_file", data_file,
        "--data_path", "f:/hqdata",
        "--block_size", "20",  # Smaller sequence
        
        # Minimal tokenizer settings
        "--mapping_strategy", "linear",  # Most predictable
        "--balancing_strategy", "none",  # Disable balancing for now
        "--n_bins", "10",  # Very small vocabulary
        "--max_token_frequency", "0.2",  # Allow higher frequency
        "--gini_threshold", "0.9",  # Relax threshold
        
        # Small model
        "--n_layer", "2",
        "--n_head", "4", 
        "--d_model", "64",
        "--time_encoding", "timeF",
        "--time_embed_type", "time_feature",
        "--pos_embed_type", "rope",
        "--dropout", "0.3",  # High dropout
        
        # Conservative training
        "--batch_size", "16",  # Small batch
        "--lr", "1e-5",  # Very low learning rate
        "--weight_decay", "0.1",  # High regularization
        "--max_epochs", "2",  # Few epochs
        "--k_folds", "2",  # Fewer folds
        "--early_stop", "2",
        "--min_delta", "1e-2",
        "--diversity_weight", "1.0",  # Very strong diversity loss
        
        # Other settings
        "--num_workers", "0",
        "--seed", "42",
        "--log_dir", "lightning_logs_quick_fix",
        "--use_class_weights"
    ]
    
    print("Running command:")
    print(" ".join(cmd))
    print("")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("\n✅ Quick fix training completed!")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Training failed with error code: {e.returncode}")
        
    except FileNotFoundError:
        print("\n❌ Training script not found")


def run_feature_reduction_test():
    """Test with only the most important feature"""
    
    print("\n=== Feature Reduction Test ===")
    print("Testing with only 'change' feature to eliminate combination issues")
    
    # Create a simple test script
    test_script = '''
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyqlab.data.dataset.dataset_bar_tokenized import BarTokenizedDataset
import pandas as pd
import numpy as np
from collections import Counter

# Override the default features
config = BarTokenizedDataset.get_default_config()
config.tokenizer.features = ['change']  # Only one feature!
config.tokenizer.n_bins = 20
config.block_size = 20
config.balance.gini_threshold = 0.9

data_file = "f:/hqdata/fut_top_min1.parquet"
if os.path.exists(data_file):
    print("Testing with single feature...")
    dataset = BarTokenizedDataset(config, data_file)
    
    print(f"Vocabulary size: {dataset.get_vocab_size()}")
    print(f"Sample count: {len(dataset)}")
    
    # Test a few samples
    if len(dataset) > 0:
        tokens = []
        for i in range(min(1000, len(dataset))):
            _, x, _, y = dataset[i]
            tokens.extend(x.numpy())
            tokens.extend(y.numpy())
        
        counter = Counter(tokens)
        unique_tokens = len(counter)
        max_freq = max(counter.values()) / len(tokens)
        
        print(f"Unique tokens used: {unique_tokens} / {dataset.get_vocab_size()}")
        print(f"Max token frequency: {max_freq:.2%}")
        
        if max_freq < 0.3 and unique_tokens > 5:
            print("✅ Single feature approach works!")
        else:
            print("⚠️  Still has issues")
            print(f"Token distribution: {dict(list(counter.most_common(10)))}")
else:
    print("Data file not found")
'''
    
    with open('test_single_feature.py', 'w') as f:
        f.write(test_script)
    
    print("Created test_single_feature.py")
    
    try:
        result = subprocess.run(["python", "test_single_feature.py"], 
                              capture_output=True, text=True, timeout=60)
        print("Output:")
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("Test timed out")
    except Exception as e:
        print(f"Test failed: {e}")


def create_emergency_config():
    """Create emergency configuration file"""
    
    config_content = '''
# Emergency configuration for prediction concentration fix

# Use this configuration if the standard approach fails

# Data settings
data_file: f:/hqdata/fut_top_min1.parquet
block_size: 15

# Tokenizer settings (MINIMAL)
mapping_strategy: linear
balancing_strategy: none
n_bins: 8  # Very small!
features: [change]  # Only one feature!
max_token_frequency: 0.3
gini_threshold: 0.95

# Model settings (TINY)
n_layer: 2
n_head: 2
d_model: 32
dropout: 0.5

# Training settings (CONSERVATIVE)
batch_size: 8
lr: 1e-6
max_epochs: 1
k_folds: 2
diversity_weight: 2.0

# Expected results:
# - Vocabulary size: 8
# - Should have 8 different tokens
# - Max frequency should be < 30%
'''
    
    with open('emergency_config.yaml', 'w') as f:
        f.write(config_content)
    
    print("Created emergency_config.yaml")


def main():
    """Main function"""
    print("=== Quick Fix for Prediction Concentration ===\n")
    
    print("Available fixes:")
    print("1. Minimal configuration training")
    print("2. Single feature test")
    print("3. Emergency configuration")
    print("")
    
    # Run minimal config training
    run_with_minimal_config()
    
    # Test single feature approach
    run_feature_reduction_test()
    
    # Create emergency config
    create_emergency_config()
    
    print(f"\n=== Summary ===")
    print(f"1. Quick fix training started with minimal settings")
    print(f"2. Single feature test created and run")
    print(f"3. Emergency configuration saved")
    print(f"")
    print(f"If the quick fix works, you should see:")
    print(f"- Prediction diversity > 10%")
    print(f"- Max prediction frequency < 30%")
    print(f"- Multiple unique predicted tokens")


if __name__ == "__main__":
    main()
