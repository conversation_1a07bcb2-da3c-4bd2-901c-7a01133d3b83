"""
Fixed BarTokenizer with improved combination methods

This fixes the token concentration problem by using better combination strategies.
"""

import numpy as np
import pandas as pd
from typing import Dict, List
import hashlib

from pyqlab.data.tokenizers.bar_tokenizer import BarTokenizer as OriginalBarTokenizer


class FixedBarTokenizer(OriginalBarTokenizer):
    """
    Fixed version of BarTokenizer with improved combination methods
    """
    
    def __init__(self, combination_method='hash', target_vocab_size=None, **kwargs):
        """
        Initialize with improved combination method
        
        Args:
            combination_method: 'hash', 'cyclic', 'weighted', or 'independent'
            target_vocab_size: Target vocabulary size for hash method
            **kwargs: Other arguments passed to parent class
        """
        super().__init__(**kwargs)
        
        # Override combination method
        self.combination_method = combination_method
        self.target_vocab_size = target_vocab_size or (self.n_bins * 2)
        
        # Recalculate vocab size based on new method
        self.vocab_size = self._calculate_vocab_size()
    
    def _calculate_vocab_size(self) -> int:
        """Calculate vocabulary size based on combination method"""
        if self.combination_method == 'hash':
            return self.target_vocab_size
        elif self.combination_method == 'cyclic':
            return self.n_bins * len(self.features)
        elif self.combination_method == 'weighted':
            return self.n_bins * len(self.features)
        elif self.combination_method == 'independent':
            # Use original method but with smaller effective size
            return min(self.n_bins * len(self.features), 200)  # Cap at 200
        else:
            return self.n_bins * len(self.features)
    
    def _combine_feature_tokens(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """Improved feature token combination"""
        if len(feature_tokens) == 1:
            return list(feature_tokens.values())[0]
        
        if self.combination_method == 'hash':
            return self._hash_combine_fixed(feature_tokens)
        elif self.combination_method == 'cyclic':
            return self._cyclic_combine(feature_tokens)
        elif self.combination_method == 'weighted':
            return self._weighted_combine(feature_tokens)
        elif self.combination_method == 'independent':
            return self._independent_combine_fixed(feature_tokens)
        else:
            # Fallback to hash method
            return self._hash_combine_fixed(feature_tokens)
    
    def _hash_combine_fixed(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Hash-based combination with deterministic results
        """
        n_samples = len(list(feature_tokens.values())[0])
        combined = np.zeros(n_samples, dtype=np.int32)
        
        # Create feature order for consistency
        feature_order = sorted(feature_tokens.keys())
        
        for i in range(n_samples):
            # Create feature tuple
            feature_tuple = tuple(feature_tokens[feature][i] for feature in feature_order)
            
            # Use deterministic hash
            hash_input = str(feature_tuple).encode('utf-8')
            hash_value = int(hashlib.md5(hash_input).hexdigest()[:8], 16)
            combined[i] = hash_value % self.target_vocab_size
        
        return combined
    
    def _cyclic_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Cyclic selection: rotate through features
        """
        n_samples = len(list(feature_tokens.values())[0])
        combined = np.zeros(n_samples, dtype=np.int32)
        
        feature_list = list(feature_tokens.keys())
        n_features = len(feature_list)
        
        for i in range(n_samples):
            # Select feature based on position
            feature_idx = i % n_features
            selected_feature = feature_list[feature_idx]
            
            # Use token value with offset
            token_value = feature_tokens[selected_feature][i]
            combined[i] = token_value + feature_idx * self.n_bins
        
        return combined
    
    def _weighted_combine(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Weighted combination with modular arithmetic
        """
        n_samples = len(list(feature_tokens.values())[0])
        combined = np.zeros(n_samples, dtype=np.int32)
        
        # Define weights for different features (importance-based)
        feature_weights = {
            'change': 5,
            'body': 4, 
            'upper_shadow': 3,
            'lower_shadow': 2,
            'volume_ratio': 1
        }
        
        for feature, tokens in feature_tokens.items():
            weight = feature_weights.get(feature, 1)
            combined += tokens * weight
        
        # Map to vocabulary range
        combined = combined % self.vocab_size
        return combined
    
    def _independent_combine_fixed(self, feature_tokens: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Fixed independent combination - select most important feature
        """
        # Priority order (most important first)
        feature_priority = ['change', 'body', 'upper_shadow', 'lower_shadow', 'volume_ratio']
        
        # Find the highest priority feature that exists
        for feature in feature_priority:
            if feature in feature_tokens:
                return feature_tokens[feature]
        
        # Fallback to first available feature
        return list(feature_tokens.values())[0]


def test_fixed_tokenizer(data_file: str):
    """Test the fixed tokenizer"""
    print("=== Testing Fixed BarTokenizer ===")
    
    # Test different combination methods
    methods = ['hash', 'cyclic', 'weighted', 'independent']
    
    for method in methods:
        print(f"\n--- Testing {method} method ---")
        
        try:
            tokenizer = FixedBarTokenizer(
                mapping_strategy='quantile',
                balancing_strategy='none',
                n_bins=20,  # Smaller bins
                features=['change', 'body', 'upper_shadow'],  # Fewer features
                combination_method=method,
                target_vocab_size=50  # Small vocab for testing
            )
            
            # Load sample data
            df = pd.read_parquet(data_file)
            first_code = df['code'].iloc[0]
            sample_df = df[df['code'] == first_code].head(200).copy()
            
            # Fit and transform
            tokens = tokenizer.fit_transform(sample_df)
            
            # Analyze results
            unique_tokens = len(np.unique(tokens))
            vocab_size = tokenizer.get_vocab_size()
            vocab_utilization = unique_tokens / vocab_size
            
            from collections import Counter
            counter = Counter(tokens)
            max_freq = max(counter.values()) / len(tokens)
            
            print(f"  Vocabulary size: {vocab_size}")
            print(f"  Unique tokens: {unique_tokens}")
            print(f"  Vocabulary utilization: {vocab_utilization:.2%}")
            print(f"  Max token frequency: {max_freq:.2%}")
            
            # Check for concentration problem
            if max_freq < 0.2 and vocab_utilization > 0.3:
                print(f"  ✅ Good diversity!")
            else:
                print(f"  ⚠️  Still has concentration issues")
                
        except Exception as e:
            print(f"  ❌ Failed: {str(e)}")


def create_training_script_with_fixed_tokenizer():
    """Create a training script using the fixed tokenizer"""
    script_content = '''"""
Training script using Fixed BarTokenizer

This script uses the improved tokenizer to solve prediction concentration.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_bar_tokenizer import FixedBarTokenizer
from train_bar_gpt4_with_tokenizer_en import main as original_main
import argparse

# Monkey patch the tokenizer
import pyqlab.data.dataset.dataset_bar_tokenized as dataset_module
dataset_module.BarTokenizer = FixedBarTokenizer

def main():
    """Main function with fixed tokenizer"""
    parser = argparse.ArgumentParser(description='Train with Fixed BarTokenizer')
    
    # Add all the same arguments as original script
    parser.add_argument('--data_file', type=str, required=True)
    parser.add_argument('--combination_method', type=str, default='hash',
                       choices=['hash', 'cyclic', 'weighted', 'independent'])
    parser.add_argument('--target_vocab_size', type=int, default=50)
    parser.add_argument('--n_bins', type=int, default=20)
    parser.add_argument('--max_epochs', type=int, default=3)
    parser.add_argument('--diversity_weight', type=float, default=0.5)
    
    args = parser.parse_args()
    
    print(f"Using Fixed BarTokenizer with {args.combination_method} combination")
    print(f"Target vocabulary size: {args.target_vocab_size}")
    
    # Set up arguments for original training function
    # (This would need to be adapted based on the actual training script)
    
    # Run training with fixed tokenizer
    # original_main(args)

if __name__ == "__main__":
    main()
'''
    
    with open('train_with_fixed_tokenizer.py', 'w') as f:
        f.write(script_content)
    
    print("Created train_with_fixed_tokenizer.py")


def main():
    """Main function"""
    data_file = "f:/hqdata/fut_top_min1.parquet"
    
    if os.path.exists(data_file):
        test_fixed_tokenizer(data_file)
    else:
        print(f"Data file not found: {data_file}")
        print("Please update the path or create test data")
    
    # Create training script
    create_training_script_with_fixed_tokenizer()
    
    print(f"\n=== Summary ===")
    print(f"1. Fixed BarTokenizer created with 4 combination methods")
    print(f"2. Hash method recommended for best diversity")
    print(f"3. Use smaller vocabulary (20-50 tokens)")
    print(f"4. Use fewer features (2-3 most important)")
    print(f"5. Training script template created")


if __name__ == "__main__":
    import os
    main()
